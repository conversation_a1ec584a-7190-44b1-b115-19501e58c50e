#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
百度米制坐标转换为经纬度坐标的Python脚本
支持BD-09MC（百度米制）转换为BD-09（百度经纬度）和WGS84坐标系统
"""

import math
import re
import json


class BaiduCoordinateConverter:
    """百度坐标转换器"""
    
    # BD-09MC转BD-09的转换参数
    MCBAND = [12890594.86, 8362377.87, 5591021, 3481989.83, 1678043.12, 0]
    MC2LL = [
        [1.410526172116255e-8, 0.00000898305509648872, -1.9939833816331, 200.9824383106796, -187.2403703815547, 91.6087516669843, -23.38765649603339, 2.57121317296198, -0.03801003308653, 17337981.2],
        [-7.435856389565537e-9, 0.000008983055097726239, -0.78625201886289, 96.32687599759846, -1.85204757529826, -59.36935905485877, 47.40033549296737, -16.50741931063887, 2.28786674699375, 10260144.86],
        [-3.030883460898826e-8, 0.00000898305509983578, 0.30071316287616, 59.74293618442277, 7.357984074871, -25.38371002664745, 13.45380521110908, -3.29883767235584, 0.32710905363475, 6856817.37],
        [-1.981981304930552e-8, 0.000008983055099779535, 0.03278182852591, 40.31678527705744, 0.65659298677277, -4.44255534477492, 0.85341911805263, 0.12923347998204, -0.04625736007561, 4482777.06],
        [3.09191371068437e-9, 0.000008983055096812155, 0.00006995724062, 23.10934304144901, -0.00023663490511, -0.6321817810242, -0.00663494467273, 0.03430082397953, -0.00466043876332, 2555164.4],
        [2.890871144776878e-9, 0.000008983055095805407, -3.068298e-8, 7.47137025468032, -0.00000353937994, -0.02145144861037, -0.00001234426596, 0.00010322952773, -0.00000323890364, 826088.5]
    ]
    
    # BD-09转WGS84的转换参数
    X_PI = 3.14159265358979324 * 3000.0 / 180.0
    PI = 3.1415926535897932384626
    A = 6378245.0
    EE = 0.00669342162296594323
    
    @classmethod
    def mc2ll(cls, x, y):
        """
        百度米制坐标转百度经纬度坐标
        
        Args:
            x (float): 百度米制X坐标
            y (float): 百度米制Y坐标
            
        Returns:
            tuple: (经度, 纬度)
        """
        x = abs(x)
        y = abs(y)
        
        # 确定使用哪个转换参数
        for i, band in enumerate(cls.MCBAND):
            if y >= band:
                mc = cls.MC2LL[i]
                break
        else:
            mc = cls.MC2LL[-1]
        
        # 执行转换计算
        lng = mc[0] + mc[1] * abs(x)
        c = abs(y) / mc[9]
        lat = mc[2] + mc[3] * c + mc[4] * c * c + mc[5] * c * c * c + \
              mc[6] * c * c * c * c + mc[7] * c * c * c * c * c + \
              mc[8] * c * c * c * c * c * c
        
        # 处理负坐标
        lng *= -1 if x < 0 else 1
        lat *= -1 if y < 0 else 1
        
        return lng, lat
    
    @classmethod
    def bd09_to_wgs84(cls, lng, lat):
        """
        BD-09坐标转WGS84坐标
        
        Args:
            lng (float): BD-09经度
            lat (float): BD-09纬度
            
        Returns:
            tuple: (WGS84经度, WGS84纬度)
        """
        # BD-09转GCJ-02
        x = lng - 0.0065
        y = lat - 0.006
        z = math.sqrt(x * x + y * y) - 0.00002 * math.sin(y * cls.X_PI)
        theta = math.atan2(y, x) - 0.000003 * math.cos(x * cls.X_PI)
        gcj_lng = z * math.cos(theta)
        gcj_lat = z * math.sin(theta)
        
        # GCJ-02转WGS84
        dlat = cls._transform_lat(gcj_lng - 105.0, gcj_lat - 35.0)
        dlng = cls._transform_lng(gcj_lng - 105.0, gcj_lat - 35.0)
        
        radlat = gcj_lat / 180.0 * cls.PI
        magic = math.sin(radlat)
        magic = 1 - cls.EE * magic * magic
        sqrtmagic = math.sqrt(magic)
        dlat = (dlat * 180.0) / ((cls.A * (1 - cls.EE)) / (magic * sqrtmagic) * cls.PI)
        dlng = (dlng * 180.0) / (cls.A / sqrtmagic * math.cos(radlat) * cls.PI)
        
        mglat = gcj_lat - dlat
        mglng = gcj_lng - dlng
        
        return mglng, mglat
    
    @classmethod
    def _transform_lat(cls, lng, lat):
        """纬度转换辅助函数"""
        ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat + \
              0.1 * lng * lat + 0.2 * math.sqrt(abs(lng))
        ret += (20.0 * math.sin(6.0 * lng * cls.PI) + 20.0 * math.sin(2.0 * lng * cls.PI)) * 2.0 / 3.0
        ret += (20.0 * math.sin(lat * cls.PI) + 40.0 * math.sin(lat / 3.0 * cls.PI)) * 2.0 / 3.0
        ret += (160.0 * math.sin(lat / 12.0 * cls.PI) + 320 * math.sin(lat * cls.PI / 30.0)) * 2.0 / 3.0
        return ret
    
    @classmethod
    def _transform_lng(cls, lng, lat):
        """经度转换辅助函数"""
        ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + \
              0.1 * lng * lat + 0.1 * math.sqrt(abs(lng))
        ret += (20.0 * math.sin(6.0 * lng * cls.PI) + 20.0 * math.sin(2.0 * lng * cls.PI)) * 2.0 / 3.0
        ret += (20.0 * math.sin(lng * cls.PI) + 40.0 * math.sin(lng / 3.0 * cls.PI)) * 2.0 / 3.0
        ret += (150.0 * math.sin(lng / 12.0 * cls.PI) + 300.0 * math.sin(lng / 30.0 * cls.PI)) * 2.0 / 3.0
        return ret


def parse_baidu_file(filename):
    """
    解析百度坐标文件
    
    Args:
        filename (str): 文件名
        
    Returns:
        list: 坐标点列表，每个元素为(x, y)元组
    """
    coordinates = []
    
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read().strip()
    
    # 使用正则表达式提取坐标对
    # 匹配形如 "数字.数字,数字.数字" 的模式
    pattern = r'(\d+\.?\d*),(\d+\.?\d*)'
    matches = re.findall(pattern, content)
    
    for match in matches:
        x, y = float(match[0]), float(match[1])
        coordinates.append((x, y))
    
    return coordinates


def convert_coordinates(input_file, output_format='bd09', output_file=None):
    """
    转换坐标文件
    
    Args:
        input_file (str): 输入文件名
        output_format (str): 输出格式，'bd09' 或 'wgs84'
        output_file (str): 输出文件名，如果为None则打印到控制台
    """
    print(f"正在读取文件: {input_file}")
    coordinates = parse_baidu_file(input_file)
    print(f"找到 {len(coordinates)} 个坐标点")
    
    converter = BaiduCoordinateConverter()
    converted_coords = []
    
    print(f"正在转换为 {output_format.upper()} 坐标系统...")
    
    for i, (x, y) in enumerate(coordinates):
        # 先转换为BD-09
        lng, lat = converter.mc2ll(x, y)
        
        # 如果需要WGS84，进一步转换
        if output_format.lower() == 'wgs84':
            lng, lat = converter.bd09_to_wgs84(lng, lat)
        
        converted_coords.append((lng, lat))
        
        if (i + 1) % 100 == 0:
            print(f"已转换 {i + 1}/{len(coordinates)} 个坐标点")
    
    # 输出结果
    if output_file:
        with open(output_file, 'w', encoding='utf-8') as f:
            for lng, lat in converted_coords:
                f.write(f"{lng},{lat}\n")
        print(f"转换结果已保存到: {output_file}")
    else:
        print(f"\n转换结果 ({output_format.upper()}):")
        print("经度,纬度")
        for lng, lat in converted_coords:
            print(f"{lng},{lat}")
    
    return converted_coords


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='百度米制坐标转换工具')
    parser.add_argument('input_file', help='输入文件名')
    parser.add_argument('-f', '--format', choices=['bd09', 'wgs84'], default='bd09',
                       help='输出坐标格式 (默认: bd09)')
    parser.add_argument('-o', '--output', help='输出文件名 (可选)')
    
    args = parser.parse_args()
    
    try:
        convert_coordinates(args.input_file, args.format, args.output)
    except FileNotFoundError:
        print(f"错误: 找不到文件 '{args.input_file}'")
    except Exception as e:
        print(f"转换过程中发生错误: {e}")


if __name__ == "__main__":
    main()
