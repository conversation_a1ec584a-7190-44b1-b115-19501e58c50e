#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的百度坐标转换脚本
直接处理baidu文件并输出转换结果
"""

from baidu_coordinate_converter import BaiduCoordinateConverter, parse_baidu_file


def main():
    """转换baidu文件中的坐标"""
    input_file = "baidu"
    
    print("=" * 50)
    print("百度米制坐标转换工具")
    print("=" * 50)
    
    try:
        # 读取并解析坐标
        print(f"正在读取文件: {input_file}")
        coordinates = parse_baidu_file(input_file)
        print(f"找到 {len(coordinates)} 个坐标点")
        
        converter = BaiduCoordinateConverter()
        
        # 转换为BD-09坐标系统
        print("\n正在转换为BD-09坐标系统...")
        bd09_coords = []
        for x, y in coordinates:
            lng, lat = converter.mc2ll(x, y)
            bd09_coords.append((lng, lat))
        
        # 转换为WGS84坐标系统
        print("正在转换为WGS84坐标系统...")
        wgs84_coords = []
        for lng, lat in bd09_coords:
            wgs84_lng, wgs84_lat = converter.bd09_to_wgs84(lng, lat)
            wgs84_coords.append((wgs84_lng, wgs84_lat))
        
        # 保存BD-09结果
        with open("baidu_bd09.txt", "w", encoding="utf-8") as f:
            f.write("经度,纬度\n")
            for lng, lat in bd09_coords:
                f.write(f"{lng},{lat}\n")
        
        # 保存WGS84结果
        with open("baidu_wgs84.txt", "w", encoding="utf-8") as f:
            f.write("经度,纬度\n")
            for lng, lat in wgs84_coords:
                f.write(f"{lng},{lat}\n")
        
        print(f"\n转换完成！")
        print(f"BD-09坐标已保存到: baidu_bd09.txt")
        print(f"WGS84坐标已保存到: baidu_wgs84.txt")
        
        # 显示前5个转换结果作为示例
        print(f"\n前5个转换结果示例:")
        print("原始坐标 (米制) -> BD-09 -> WGS84")
        print("-" * 80)
        for i in range(min(5, len(coordinates))):
            orig_x, orig_y = coordinates[i]
            bd09_lng, bd09_lat = bd09_coords[i]
            wgs84_lng, wgs84_lat = wgs84_coords[i]
            print(f"{orig_x:>12.2f},{orig_y:>12.2f} -> {bd09_lng:>10.6f},{bd09_lat:>9.6f} -> {wgs84_lng:>10.6f},{wgs84_lat:>9.6f}")
        
        if len(coordinates) > 5:
            print(f"... 还有 {len(coordinates) - 5} 个坐标点")
            
    except FileNotFoundError:
        print(f"错误: 找不到文件 '{input_file}'")
        print("请确保baidu文件存在于当前目录中")
    except Exception as e:
        print(f"转换过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
