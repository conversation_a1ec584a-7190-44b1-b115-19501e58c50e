<!DOCTYPE html>
<html>
  <head>
    <title>test6.html</title>
    <meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
    <!-- 不需要 jQuery，使用原生 JavaScript -->
    <script
      type="text/javascript"
      src="http://api.map.baidu.com/api?v=2.0&ak=1XjLLEhZhQNUzd93EjU5nOGQ"
    ></script>
  </head>

  <body>
    <div id="allmap" style="width: 100%; height: 600px"></div>
    <div class="layout">
      <input id="value" value="淮海工学院东港学院-郁洲书院" type="text" />
      　　 <input type="submit" onclick="pd()" value="定位" />
    </div>
  </body>
  <script type="text/javascript">
    //初始化地图s
    var value = "";
    var map = new BMap.Map("allmap");
    map.centerAndZoom("连云港市", 10);
    map.enableScrollWheelZoom();

    //定位区域，小地名，使用本地检索方法
    var dw = function () {
      var local = new BMap.LocalSearch(map, {
        renderOptions: { map: map },
      });
      local.setMarkersSetCallback(function (pois) {
        console.log(pois);
        //清除所有覆盖物后，在叠加第一个点
        map.clearOverlays();
        for (var i = 0; i < pois.length; i++) {
          var marker = new BMap.Marker(pois[i].point);
          map.addOverlay(marker);
        }
        //根据获取到的poi id，查询边界坐标集合，画多边形
        var uid = pois[0].uid;
        queryUid(uid);
      });
      local.search(value);
      map.clearOverlays();
    };

    //获取小区信息 - 使用原生 JavaScript 和 JSONP
    function queryUid(uid) {
      var url = "http://map.baidu.com/?pcevaname=pc4.1&qt=ext&ext_ver=new&l=12&uid=" + uid;
      
      // 创建 JSONP 请求
      var script = document.createElement('script');
      var callbackName = 'jsonp_callback_' + Math.round(100000 * Math.random());
      
      // 定义回调函数
      window[callbackName] = function(result) {
        try {
          var content = result.content;
          if (null != content.geo && content.geo != undefined) {
            var geo = content.geo;
            var points = coordinateToPoints(geo);
            //point分组，得到多边形的每一个点，画多边形
            if (points && points.indexOf(";") >= 0) {
              points = points.split(";");
            }
            var arr = [];
            for (var i = 0; i < points.length - 1; i++) {
              var temp = points[i].split(",");
              arr.push(
                new BMap.Point(parseFloat(temp[0]), parseFloat(temp[1]))
              );
            }
            var polygon = new BMap.Polygon(arr, {
              strokeColor: "blue",
              strokeWeight: 2,
              strokeOpacity: 0.5,
            }); //创建多边形
            map.addOverlay(polygon); //增加多边形
          }
        } catch (error) {
          console.error("处理响应时出错:", error);
        } finally {
          // 清理
          document.head.removeChild(script);
          delete window[callbackName];
        }
      };
      
      // 设置超时
      setTimeout(function() {
        if (window[callbackName]) {
          console.error("请求超时");
          document.head.removeChild(script);
          delete window[callbackName];
        }
      }, 3000);
      
      script.src = url + '&callback=' + callbackName;
      document.head.appendChild(script);
    }

    //获取边界
    function getBoundary() {
      //获取边界
      var bdary = new BMap.Boundary();
      bdary.get(value, function (rs) {
        //获取行政区域
        map.clearOverlays(); //清除地图覆盖物
        var count = rs.boundaries.length; //行政区域的点有多少个
        for (var i = 0; i < count; i++) {
          var ply = new BMap.Polygon(rs.boundaries[i], {
            strokeWeight: 1,
            strokeColor: "#ff0000",
          }); //建立多边形覆盖物
          map.addOverlay(ply); //添加覆盖物
          map.setViewport(ply.getPath()); //调整视野
        }
      });
    }
    //正则表达式，满足条件后调用
    var patter = /['省'|'市'|'区'|'县']$/;
    var pd = function () {
      value = document.getElementById("value").value;
      if (patter.test(value) == true) {
        //关键字结尾是省市县区就调用下面方法
        getBoundary();
        if (/社区|小区$/.test(value) == true) {
          //因为区后面结尾，会有小区和社区，即做了一个字方法
          dw();
        }
      } else {
        //关键字结尾没有省市县区结尾就调用此方法
        dw();
      }
    };

    //坐标转换
    function coordinateToPoints(coordinate) {
      console.log(coordinate);
      var points = "";
      if (coordinate) {
        var projection = BMAP_NORMAL_MAP.getProjection();

        if (coordinate && coordinate.indexOf("-") >= 0) {
          coordinate = coordinate.split("-");
        }
        //取点集合
        var tempco = coordinate[1];
        if (tempco && tempco.indexOf(",") >= 0) {
          tempco = tempco.replace(";", "").split(",");
        }
        //分割点，两个一组，组成百度米制坐标
        var temppoints = [];
        for (var i = 0, len = tempco.length; i < len; i++) {
          var obj = new Object();
          obj.lng = tempco[i];
          obj.lat = tempco[i + 1];
          temppoints.push(obj);
          i++;
        }
        //遍历米制坐标，转换为经纬度
        for (var i = 0, len = temppoints.length; i < len; i++) {
          //var pos = coordinate[i].split(',');
          var pos = temppoints[i];
          var point = projection.pointToLngLat(
            new BMap.Pixel(pos.lng, pos.lat)
          );
          points += [point.lng, point.lat].toString() + ";";
        }
      }
      return points;
    }
  </script>
</html>
